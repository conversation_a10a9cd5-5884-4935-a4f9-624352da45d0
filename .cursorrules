# Instructions

During your interaction with the user, if you find anything reusable in this project (e.g. version of a library, model name), especially about a fix to a mistake you made or a correction you received, you should take note in the `Lessons` section in the `.cursorrules` file so you will not make the same mistake again. 

You should also use the `.cursorrules` file as a Scratchpad to organize your thoughts. Especially when you receive a new task, you should first review the content of the Scratchpad, clear old different task if necessary, first explain the task, and plan the steps you need to take to complete the task. You can use todo markers to indicate the progress, e.g.
[X] Task 1
[ ] Task 2

Also update the progress of the task in the Scratchpad when you finish a subtask.
Especially when you finished a milestone, it will help to improve your depth of task accomplishment to use the Scratchpad to reflect and plan.
The goal is to help you maintain a big picture as well as the progress of the task. Always refer to the Scratchpad when you plan the next step.

# Lessons

## User Specified Lessons

- You have a python venv in ./venv. Always use (activate) it when doing python development. First, to check whether 'uv' is available, use `which uv`. If that's the case, first activate the venv, and then use `uv pip install` to install packages. Otherwise, fall back to `pip`.
- Due to <PERSON>urs<PERSON>'s limit, when you use `git` and `gh` and need to submit a multiline commit message, first write the message in a file, and then use `git commit -F <filename>` or similar command to commit. And then remove the file. Include "[Cursor] " in the commit message and PR title.

## Cursor learned

- For search results, ensure proper handling of different character encodings (UTF-8) for international queries
- When using seaborn styles in matplotlib, use 'seaborn-v0_8' instead of 'seaborn' as the style name due to recent seaborn version changes
- The user is using Windows and cannot use "&&" to connect two commands.
- 搜索功能显示NaN问题：search_students方法需要返回完整的成绩字段（total_score, academic_score等），而不仅仅是基本信息
- AI功能权限控制：前端模板使用`{% if not current_user.is_student() %}`条件判断，学生用户看不到AI功能区域是正常的权限控制
- 年级筛选：需要在模型的get_ranking_data方法中添加grade参数支持，并在所有相关API路由中传递此参数
- 前端NaN显示问题：JavaScript中parseFloat()处理null/undefined时返回NaN，需要添加空值检查
- 浏览器缓存问题：前端模板修改后需要禁用缓存或强制刷新才能看到效果
- 列配置映射问题：前端列配置需要精确对应表格实际结构，避免索引错位导致显隐失效
- AI API端点配置：DeepSeek官方API端点为 `https://api.deepseek.com/chat/completions`，不同于阿里云代理
- 多字段排序实现：使用JSON序列化在前后端传递复杂排序参数，提高排序功能灵活性
- 重置功能完整性：重置操作需要同时清理表单、配置存储和视觉状态，确保一致性

# Scratchpad

基于 prd.txt 技术文档，需要实现一个完整的学生学业数据管理与分析平台，包含：
- 后端：Python Flask
- 前端：HTML + CSS + JS
- 图表：ECharts
- 设计风格：蓝白色调现代化界面
- 现有基础：scholarship_analyzer.py（数据解析）+ scholarship_data.db（SQLite数据库）

### 主要功能模块
1. 排行榜显示（支持排序、导出、AI辅助SQL查询）
2. 详情与分析（学生个人分析、智能分析报告）
3. 主面板（数据概览、统计信息）
4. API管理系统（deepseek-r1模型配置）
5. 数据库日志系统
6. 导入导出模块
7. 班级分析页面
8. 用户管理（管理员/教师/学生权限）

### 开发计划
[X] 1. 项目结构设计和初始化
[X] 2. Flask后端框架搭建
[X] 3. 用户认证和权限管理系统
[X] 4. 数据库扩展（用户表、日志表等）
[X] 5. 排行榜功能实现（完整功能：多字段排序、筛选、导出、AI辅助SQL查询）
[ ] 6. 学生详情和分析功能
[ ] 7. 主面板和统计功能
[X] 8. AI集成（deepseek-r1）- AI服务框架已集成，需修复API密钥问题
[ ] 9. 班级分析功能
[ ] 10. 导入导出功能
[ ] 11. 前端界面开发
[ ] 12. 测试和优化

## 当前任务：修复AI对话和排序功能问题

### 新任务：修复两个关键问题

🔧 **1. AI对话功能异常**
- 问题：AI服务端点配置错误，显示404错误
- 原因：API端点缺少/chat/completions路径
- 解决：修复services/ai_service.py中的端点配置逻辑，确保正确拼接API路径
- 状态：[X] 已修复 - AI服务现在工作正常

🔧 **2. 排行榜排序功能异常**
- 问题：选择不同排序选项时，排行榜显示结果相同
- 原因：前端排序参数没有正确传递到API请求中
- 解决：修复loadRankingData函数，确保排序参数正确传递
- 状态：[X] 已修复 - 排序参数现在正确传递

### 技术要点
- 使用Bootstrap 5 + ECharts
- 遵循蓝白色调设计风格
- 响应式设计
- 权限控制：学生只能看到自己的数据
- 默认管理员：admin / admin123
- AI功能权限：仅管理员和教师可使用AI SQL助手
- 数据查询安全：SQL执行严格限制为SELECT查询，禁止修改操作
- 年级提取：从班级名称数字部分提取（如通信工程2101班 → 21级）

## 修复总结

### 已完成的两个关键问题修复：

1. **AI对话功能异常修复**：
   - **问题**：AI服务端点配置错误，返回404错误
   - **根本原因**：API端点缺少`/chat/completions`路径
   - **解决方案**：
     * 修复`services/ai_service.py`中的端点配置逻辑
     * 自动拼接正确的API路径
     * 更新`config.py`中的默认配置
   - **验证结果**：✅ AI服务现在工作正常，能够正确生成SQL和进行对话

2. **排行榜排序功能异常修复**：
   - **问题**：选择不同排序选项时，排行榜显示结果相同
   - **根本原因**：前端排序参数没有正确传递到API请求中
   - **解决方案**：
     * 修复`loadRankingData`函数，确保排序参数正确传递
     * 保持多字段排序JSON格式支持
     * 维护向后兼容性
   - **验证结果**：✅ 排序功能现在工作正常，能够正确按指定字段排序

### 技术改进：
- API端点自动拼接逻辑，提高配置灵活性
- 前端排序参数传递机制优化
- 完整的功能测试脚本（test_fixes.py）
- 详细的错误日志和状态检查