{% extends "base.html" %}

{% block title %}学生详情分析 - 学生学业数据管理与分析平台{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="h3 mb-4">
            <i class="fas fa-user-graduate me-2"></i>
            学生详情分析
        </h1>
    </div>
</div>

{% if student_data %}
<!-- 学生基本信息 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-id-card me-2"></i>
                    基本信息
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-2">
                        <strong>学号：</strong>{{ student_data[0].student_id }}
                    </div>
                    <div class="col-md-2">
                        <strong>姓名：</strong>{{ student_data[0].name }}
                    </div>
                    <div class="col-md-3">
                        <strong>专业：</strong>{{ student_data[0].major_name }}
                    </div>
                    <div class="col-md-2">
                        <strong>班级：</strong>{{ student_data[0].class_name }}
                    </div>
                    <div class="col-md-3">
                        <strong>数据记录：</strong>{{ student_data|length }} 个学期
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 成绩趋势分析 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-line me-2"></i>
                    成绩趋势分析
                </h5>
            </div>
            <div class="card-body">
                <div id="trendChart" style="height: 400px;"></div>
            </div>
        </div>
    </div>
</div>

<!-- 排名趋势分析 -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-trophy me-2"></i>
                    排名趋势
                </h5>
            </div>
            <div class="card-body">
                <div id="rankTrendChart" style="height: 300px;"></div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-pie me-2"></i>
                    最新成绩构成
                </h5>
            </div>
            <div class="card-body">
                <div id="scoreCompositionChart" style="height: 300px;"></div>
            </div>
        </div>
    </div>
</div>

<!-- AI智能分析 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-robot me-2"></i>
                    AI智能分析
                </h5>
            </div>
            <div class="card-body">
                <button type="button" class="btn btn-primary" id="aiAnalysisBtn">
                    <i class="fas fa-magic me-1"></i>
                    生成AI分析报告
                </button>
                
                <div id="aiAnalysisResult" class="mt-3" style="display: none;">
                    <div class="ai-analysis">
                        <h6><i class="fas fa-brain me-2"></i>AI分析结果</h6>
                        <div id="aiAnalysisContent">
                            <!-- AI分析内容将在这里显示 -->
                        </div>
                    </div>
                </div>
                
                <div id="aiAnalysisLoading" class="text-center mt-3" style="display: none;">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">AI分析中...</span>
                    </div>
                    <p class="mt-2">AI正在分析学生数据，请稍候...</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 详细成绩记录 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-table me-2"></i>
                    详细成绩记录
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>学年学期</th>
                                <th>总分</th>
                                <th>总排名</th>
                                <th>学业成绩</th>
                                <th>学业排名</th>
                                <th>综合素质分</th>
                                <th>德育分</th>
                                <th>社会工作</th>
                                <th>科研分</th>
                                <th>活动分</th>
                                <th>集体分</th>
                                <th>奖学金等级</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for record in student_data %}
                            <tr>
                                <td>{{ record.academic_year }}-{{ record.semester }}</td>
                                <td>
                                    <span class="badge bg-primary">{{ "%.2f"|format(record.total_score) }}</span>
                                </td>
                                <td>
                                    {% if record.total_rank %}
                                        {% if record.total_rank <= 3 %}
                                            <span class="badge bg-warning">{{ record.total_rank }}</span>
                                        {% elif record.total_rank <= 10 %}
                                            <span class="badge bg-info">{{ record.total_rank }}</span>
                                        {% else %}
                                            <span class="badge bg-secondary">{{ record.total_rank }}</span>
                                        {% endif %}
                                    {% else %}
                                        <span class="text-muted">N/A</span>
                                    {% endif %}
                                </td>
                                <td>{{ "%.2f"|format(record.academic_score) }}</td>
                                <td>
                                    {% if record.academic_rank %}
                                        <span class="badge bg-success">{{ record.academic_rank }}</span>
                                    {% else %}
                                        <span class="text-muted">N/A</span>
                                    {% endif %}
                                </td>
                                <td>{{ "%.2f"|format(record.comprehensive_score) }}</td>
                                <td>{{ "%.2f"|format(record.moral_score or 0) }}</td>
                                <td>{{ "%.2f"|format(record.social_work_score or 0) }}</td>
                                <td>{{ "%.2f"|format(record.research_score or 0) }}</td>
                                <td>{{ "%.2f"|format(record.activity_total_score or 0) }}</td>
                                <td>{{ "%.2f"|format(record.collective_score or 0) }}</td>
                                <td>
                                    {% if record.award_level %}
                                        {% if record.award_level == '一等奖学金' %}
                                            <span class="badge bg-warning">{{ record.award_level }}</span>
                                        {% elif record.award_level == '二等奖学金' %}
                                            <span class="badge bg-info">{{ record.award_level }}</span>
                                        {% elif record.award_level == '三等奖学金' %}
                                            <span class="badge bg-success">{{ record.award_level }}</span>
                                        {% else %}
                                            <span class="badge bg-secondary">{{ record.award_level }}</span>
                                        {% endif %}
                                    {% else %}
                                        <span class="text-muted">无</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

{% else %}
<div class="row">
    <div class="col-12">
        <div class="alert alert-warning">
            <i class="fas fa-exclamation-triangle me-2"></i>
            未找到该学生的数据。
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_scripts %}
<script>
$(document).ready(function() {
    {% if student_data %}
    // 绘制图表
    drawTrendChart();
    drawRankTrendChart();
    drawScoreCompositionChart();
    
    // AI分析按钮事件
    $('#aiAnalysisBtn').on('click', function() {
        generateAIAnalysis();
    });
    {% endif %}
});

{% if student_data %}
function drawTrendChart() {
    const chart = echarts.init(document.getElementById('trendChart'));
    const data = {{ student_data | tojson }};
    
    const option = {
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'cross'
            }
        },
        legend: {
            data: ['总分', '学业成绩', '综合素质分']
        },
        xAxis: {
            type: 'category',
            data: data.map(item => item.academic_year + '-' + item.semester)
        },
        yAxis: {
            type: 'value',
            name: '分数'
        },
        series: [
            {
                name: '总分',
                type: 'line',
                data: data.map(item => item.total_score.toFixed(2)),
                itemStyle: { color: '#4facfe' },
                smooth: true
            },
            {
                name: '学业成绩',
                type: 'line',
                data: data.map(item => item.academic_score.toFixed(2)),
                itemStyle: { color: '#28a745' },
                smooth: true
            },
            {
                name: '综合素质分',
                type: 'line',
                data: data.map(item => item.comprehensive_score.toFixed(2)),
                itemStyle: { color: '#ffc107' },
                smooth: true
            }
        ]
    };
    chart.setOption(option);
}

function drawRankTrendChart() {
    const chart = echarts.init(document.getElementById('rankTrendChart'));
    const data = {{ student_data | tojson }};
    
    const option = {
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'cross'
            }
        },
        legend: {
            data: ['总排名', '学业排名']
        },
        xAxis: {
            type: 'category',
            data: data.map(item => item.academic_year + '-' + item.semester)
        },
        yAxis: {
            type: 'value',
            name: '排名',
            inverse: true, // 排名越小越好，所以反转Y轴
            min: function(value) {
                return Math.max(1, value.min - 5);
            }
        },
        series: [
            {
                name: '总排名',
                type: 'line',
                data: data.map(item => item.total_rank || null),
                itemStyle: { color: '#4facfe' },
                smooth: true,
                connectNulls: false
            },
            {
                name: '学业排名',
                type: 'line',
                data: data.map(item => item.academic_rank || null),
                itemStyle: { color: '#28a745' },
                smooth: true,
                connectNulls: false
            }
        ]
    };
    chart.setOption(option);
}

function drawScoreCompositionChart() {
    const chart = echarts.init(document.getElementById('scoreCompositionChart'));
    const latestData = {{ student_data[-1] | tojson }};
    
    const option = {
        tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        series: [{
            name: '成绩构成',
            type: 'pie',
            radius: '70%',
            data: [
                { name: '德育分', value: latestData.moral_score || 0 },
                { name: '社会工作分', value: latestData.social_work_score || 0 },
                { name: '科研分', value: latestData.research_score || 0 },
                { name: '活动分', value: latestData.activity_total_score || 0 },
                { name: '集体分', value: latestData.collective_score || 0 }
            ],
            emphasis: {
                itemStyle: {
                    shadowBlur: 10,
                    shadowOffsetX: 0,
                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
            }
        }]
    };
    chart.setOption(option);
}

function generateAIAnalysis() {
    $('#aiAnalysisBtn').prop('disabled', true);
    $('#aiAnalysisLoading').show();
    $('#aiAnalysisResult').hide();
    
    $.ajax({
        url: '/analysis/api/student/{{ student_id }}/ai_analysis',
        method: 'POST',
        contentType: 'application/json',
        success: function(response) {
            if (response.success) {
                $('#aiAnalysisContent').html(response.data.analysis);
                $('#aiAnalysisResult').show();
            } else {
                alert('AI分析失败: ' + response.error);
            }
        },
        error: function() {
            alert('AI分析失败，请稍后重试');
        },
        complete: function() {
            $('#aiAnalysisBtn').prop('disabled', false);
            $('#aiAnalysisLoading').hide();
        }
    });
}
{% endif %}
</script>
{% endblock %}
