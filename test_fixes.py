#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复效果的脚本
"""

import requests
import json
from services.ai_service import AIService
from models.scholarship import ScholarshipData

def test_ai_service():
    """测试AI服务"""
    print("=" * 50)
    print("测试AI服务")
    print("=" * 50)
    
    try:
        ai = AIService()
        print(f"API URL: {ai.api_url}")
        print(f"API Key: {ai.api_key[:10]}..." if ai.api_key else "None")
        print(f"Model: {ai.model}")
        
        # 测试简单对话
        print("\n测试AI对话...")
        result = ai._make_request([
            {"role": "user", "content": "你好，请简单回复"}
        ])
        print(f"AI响应: {result}")
        
        # 测试SQL生成
        print("\n测试SQL生成...")
        sql_result = ai.generate_sql_query("查询总分前5名的学生")
        print(f"生成的SQL: {sql_result}")
        
        print("✅ AI服务测试通过")
        return True
        
    except Exception as e:
        print(f"❌ AI服务测试失败: {e}")
        return False

def test_sorting_functionality():
    """测试排序功能"""
    print("=" * 50)
    print("测试排序功能")
    print("=" * 50)
    
    try:
        scholarship_data = ScholarshipData()
        
        # 测试单字段排序
        print("测试单字段排序（总分降序）...")
        data1 = scholarship_data.get_ranking_data(
            order_by='total_score',
            order_desc=True,
            limit=5
        )
        print(f"获取到 {len(data1)} 条记录")
        if data1:
            print("前3名学生:")
            for i, student in enumerate(data1[:3]):
                print(f"  {i+1}. {student['name']} - 总分: {student['total_score']}")
        
        # 测试多字段排序
        print("\n测试多字段排序（总分降序，学业成绩降序）...")
        sort_fields_json = json.dumps([
            {"field": "total_score", "direction": "desc"},
            {"field": "academic_score", "direction": "desc"}
        ])
        
        data2 = scholarship_data.get_ranking_data(
            sort_fields=sort_fields_json,
            limit=5
        )
        print(f"获取到 {len(data2)} 条记录")
        if data2:
            print("前3名学生:")
            for i, student in enumerate(data2[:3]):
                print(f"  {i+1}. {student['name']} - 总分: {student['total_score']}, 学业: {student['academic_score']}")
        
        # 验证排序是否生效
        if len(data1) >= 2 and len(data2) >= 2:
            # 检查是否按总分降序排列
            is_sorted = all(
                float(data1[i]['total_score'] or 0) >= float(data1[i+1]['total_score'] or 0)
                for i in range(len(data1)-1)
            )
            print(f"\n排序验证: {'✅ 排序正确' if is_sorted else '❌ 排序错误'}")
            return is_sorted
        else:
            print("✅ 排序功能基本正常（数据不足以验证）")
            return True
            
    except Exception as e:
        print(f"❌ 排序功能测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试修复效果...")
    
    # 测试AI服务
    ai_success = test_ai_service()
    
    # 测试排序功能
    sort_success = test_sorting_functionality()
    
    # 总结
    print("=" * 50)
    print("测试总结")
    print("=" * 50)
    print(f"AI服务: {'✅ 正常' if ai_success else '❌ 异常'}")
    print(f"排序功能: {'✅ 正常' if sort_success else '❌ 异常'}")
    
    if ai_success and sort_success:
        print("\n🎉 所有功能修复成功！")
    else:
        print("\n⚠️ 部分功能仍有问题，需要进一步检查")

if __name__ == "__main__":
    main()
