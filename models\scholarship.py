#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
奖学金数据模型
基于现有的scholarship_analyzer.py，提供Web应用所需的数据访问接口
"""

import sqlite3
from models.database import get_db_connection, get_db_cursor
from scholarship_analyzer import ScholarshipAnalyzer

class ScholarshipData:
    """奖学金数据访问类"""
    
    def __init__(self, db_path='scholarship_data.db'):
        self.db_path = db_path
        self.analyzer = ScholarshipAnalyzer(db_path)
    
    def get_ranking_data(self, academic_year=None, semester=None, major=None, grade=None,
                        order_by='total_score', order_desc=True, sort_fields=None, limit=None, offset=0):
        """获取排行榜数据"""
        try:
            with get_db_cursor(self.db_path) as cursor:
                # 构建查询语句
                query = '''
                    SELECT 
                        s.student_id, s.name, sem.academic_year, sem.semester, m.major_name,
                        sc.class_name, sc.grade, sc.comprehensive_score, sc.academic_score,
                        sc.total_score, sc.total_rank, sc.award_level, sc.remarks,
                        sc.moral_score, sc.social_work_score, sc.research_score,
                        sc.activity_total_score, sc.collective_score,
                        sc.academic_rank, sc.academic_percentage
                    FROM scores sc
                    JOIN students s ON sc.student_id = s.student_id
                    JOIN semesters sem ON sc.semester_id = sem.id
                    JOIN majors m ON sc.major_id = m.id
                    WHERE 1=1
                '''
                
                params = []
                if academic_year:
                    query += ' AND sem.academic_year = ?'
                    params.append(academic_year)
                if semester:
                    query += ' AND sem.semester = ?'
                    params.append(semester)
                if major:
                    query += ' AND m.major_name LIKE ?'
                    params.append(f'%{major}%')
                if grade:
                    query += ' AND sc.grade = ?'
                    params.append(grade)
                
                # 排序
                if sort_fields:
                    # 解析多字段排序JSON
                    import json
                    try:
                        sort_list = json.loads(sort_fields)
                        order_clauses = []
                        for sort_item in sort_list:
                            field = sort_item.get('field', 'total_score')
                            direction = 'DESC' if sort_item.get('direction', 'desc') == 'desc' else 'ASC'
                            order_clauses.append(f'sc.{field} {direction}')
                        
                        if order_clauses:
                            query += f' ORDER BY {", ".join(order_clauses)}'
                        else:
                            # 如果JSON解析失败，使用默认排序
                            order_direction = 'DESC' if order_desc else 'ASC'
                            query += f' ORDER BY sc.{order_by} {order_direction}'
                    except (json.JSONDecodeError, KeyError, TypeError):
                        # JSON解析失败，使用默认排序
                        order_direction = 'DESC' if order_desc else 'ASC'
                        query += f' ORDER BY sc.{order_by} {order_direction}'
                else:
                    # 使用单字段排序
                    order_direction = 'DESC' if order_desc else 'ASC'
                    query += f' ORDER BY sc.{order_by} {order_direction}'
                
                # 分页
                if limit:
                    query += ' LIMIT ? OFFSET ?'
                    params.extend([limit, offset])
                
                cursor.execute(query, params)
                return [dict(row) for row in cursor.fetchall()]
                
        except Exception as e:
            print(f"获取排行榜数据失败: {e}")
            return []
    
    def get_student_detail(self, student_id):
        """获取学生详细信息"""
        try:
            with get_db_cursor(self.db_path) as cursor:
                cursor.execute('''
                    SELECT 
                        s.student_id, s.name, sem.academic_year, sem.semester, m.major_name,
                        sc.class_name, sc.grade, sc.comprehensive_score, sc.academic_score,
                        sc.total_score, sc.total_rank, sc.award_level, sc.remarks,
                        sc.moral_score, sc.social_work_score, sc.research_score,
                        sc.activity_base_score, sc.activity_exercise_score, sc.activity_event_score,
                        sc.activity_total_score, sc.collective_score,
                        sc.academic_rank, sc.academic_percentage,
                        sc.comprehensive_weighted, sc.academic_weighted
                    FROM scores sc
                    JOIN students s ON sc.student_id = s.student_id
                    JOIN semesters sem ON sc.semester_id = sem.id
                    JOIN majors m ON sc.major_id = m.id
                    WHERE s.student_id = ?
                    ORDER BY sem.academic_year, sem.semester
                ''', (student_id,))
                
                return [dict(row) for row in cursor.fetchall()]
                
        except Exception as e:
            print(f"获取学生详细信息失败: {e}")
            return []
    
    def get_class_analysis_data(self, academic_year, semester, major=None):
        """获取班级分析数据"""
        try:
            with get_db_cursor(self.db_path) as cursor:
                query = '''
                    SELECT 
                        sc.class_name,
                        COUNT(*) as student_count,
                        AVG(sc.total_score) as avg_total_score,
                        AVG(sc.academic_score) as avg_academic_score,
                        AVG(sc.comprehensive_score) as avg_comprehensive_score,
                        MAX(sc.total_score) as max_total_score,
                        MIN(sc.total_score) as min_total_score
                    FROM scores sc
                    JOIN semesters sem ON sc.semester_id = sem.id
                    JOIN majors m ON sc.major_id = m.id
                    WHERE sem.academic_year = ? AND sem.semester = ?
                '''
                
                params = [academic_year, semester]
                if major:
                    query += ' AND m.major_name LIKE ?'
                    params.append(f'%{major}%')
                
                query += ' GROUP BY sc.class_name ORDER BY avg_total_score DESC'
                
                cursor.execute(query, params)
                return [dict(row) for row in cursor.fetchall()]
                
        except Exception as e:
            print(f"获取班级分析数据失败: {e}")
            return []
    
    def get_statistics(self):
        """获取统计信息"""
        return self.analyzer.get_statistics()
    
    def get_available_filters(self):
        """获取可用的筛选条件"""
        try:
            with get_db_cursor(self.db_path) as cursor:
                # 获取学年学期
                cursor.execute('''
                    SELECT DISTINCT academic_year, semester 
                    FROM semesters 
                    ORDER BY academic_year DESC, semester DESC
                ''')
                semesters = [dict(row) for row in cursor.fetchall()]
                
                # 获取专业
                cursor.execute('SELECT DISTINCT major_name FROM majors ORDER BY major_name')
                majors = [row['major_name'] for row in cursor.fetchall()]
                
                # 获取年级
                cursor.execute('SELECT DISTINCT grade FROM scores ORDER BY grade DESC')
                grades = [row['grade'] for row in cursor.fetchall()]
                
                return {
                    'semesters': semesters,
                    'majors': majors,
                    'grades': grades
                }
                
        except Exception as e:
            print(f"获取筛选条件失败: {e}")
            return {'semesters': [], 'majors': [], 'grades': []}
    
    def search_students(self, keyword, academic_year=None, semester=None):
        """搜索学生"""
        try:
            with get_db_cursor(self.db_path) as cursor:
                query = '''
                    SELECT 
                        s.student_id, s.name, sem.academic_year, sem.semester, m.major_name,
                        sc.class_name, sc.grade, sc.comprehensive_score, sc.academic_score,
                        sc.total_score, sc.total_rank, sc.award_level, sc.remarks,
                        sc.moral_score, sc.social_work_score, sc.research_score,
                        sc.activity_total_score, sc.collective_score,
                        sc.academic_rank, sc.academic_percentage
                    FROM scores sc
                    JOIN students s ON sc.student_id = s.student_id
                    JOIN semesters sem ON sc.semester_id = sem.id
                    JOIN majors m ON sc.major_id = m.id
                    WHERE (s.student_id LIKE ? OR s.name LIKE ?)
                '''
                
                params = [f'%{keyword}%', f'%{keyword}%']
                
                if academic_year:
                    query += ' AND sem.academic_year = ?'
                    params.append(academic_year)
                if semester:
                    query += ' AND sem.semester = ?'
                    params.append(semester)
                
                query += ' ORDER BY s.student_id, sem.academic_year DESC, sem.semester DESC'
                
                cursor.execute(query, params)
                return [dict(row) for row in cursor.fetchall()]
                
        except Exception as e:
            print(f"搜索学生失败: {e}")
            return []
    
    def get_grade_distribution(self, academic_year, semester, major=None):
        """获取成绩分布数据"""
        try:
            with get_db_cursor(self.db_path) as cursor:
                query = '''
                    SELECT 
                        CASE 
                            WHEN sc.total_score >= 90 THEN '90-100'
                            WHEN sc.total_score >= 80 THEN '80-89'
                            WHEN sc.total_score >= 70 THEN '70-79'
                            WHEN sc.total_score >= 60 THEN '60-69'
                            ELSE '60以下'
                        END as score_range,
                        COUNT(*) as count
                    FROM scores sc
                    JOIN semesters sem ON sc.semester_id = sem.id
                    JOIN majors m ON sc.major_id = m.id
                    WHERE sem.academic_year = ? AND sem.semester = ?
                '''
                
                params = [academic_year, semester]
                if major:
                    query += ' AND m.major_name LIKE ?'
                    params.append(f'%{major}%')
                
                query += ' GROUP BY score_range ORDER BY MIN(sc.total_score) DESC'
                
                cursor.execute(query, params)
                return [dict(row) for row in cursor.fetchall()]
                
        except Exception as e:
            print(f"获取成绩分布失败: {e}")
            return []
