{% extends "base.html" %}

{% block title %}我的排名 - 学生学业数据管理与分析平台{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="h3 mb-4">
            <i class="fas fa-trophy me-2"></i>
            我的排名记录
        </h1>
    </div>
</div>

<!-- 筛选条件 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-filter me-2"></i>
                    筛选条件
                </h5>
            </div>
            <div class="card-body">
                <form id="filterForm" class="row g-3">
                    <div class="col-md-4">
                        <label for="academicYear" class="form-label">学年</label>
                        <select class="form-select" id="academicYear" name="academic_year">
                            <option value="">全部学年</option>
                            {% for semester in filters.semesters %}
                                <option value="{{ semester.academic_year }}">{{ semester.academic_year }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="col-md-4">
                        <label for="semester" class="form-label">学期</label>
                        <select class="form-select" id="semester" name="semester">
                            <option value="">全部学期</option>
                            <option value="1">第一学期</option>
                            <option value="2">第二学期</option>
                        </select>
                    </div>
                    
                    <div class="col-md-4">
                        <button type="submit" class="btn btn-primary mt-4">
                            <i class="fas fa-search me-1"></i>查询
                        </button>
                        <button type="button" class="btn btn-outline-secondary mt-4 ms-2" id="resetBtn">
                            <i class="fas fa-undo me-1"></i>重置
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- 我的排名数据 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list me-2"></i>
                    我的历史排名
                </h5>
            </div>
            <div class="card-body">
                <div id="loadingSpinner" class="text-center" style="display: none;">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p class="mt-2">正在加载数据...</p>
                </div>
                
                <div class="table-responsive">
                    <table class="table table-hover" id="rankingTable">
                        <thead>
                            <tr>
                                <th>学年学期</th>
                                <th>总分</th>
                                <th>总排名</th>
                                <th>学业成绩</th>
                                <th>学业排名</th>
                                <th>综合素质分</th>
                                <th>奖学金等级</th>
                                <th>备注</th>
                            </tr>
                        </thead>
                        <tbody id="rankingTableBody">
                            <!-- 数据将通过JavaScript动态加载 -->
                        </tbody>
                    </table>
                </div>
                
                <div class="mt-3">
                    <button type="button" class="btn btn-success" id="exportBtn">
                        <i class="fas fa-download me-1"></i>导出我的成绩
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 排名趋势图表 -->
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-line me-2"></i>
                    排名趋势
                </h5>
            </div>
            <div class="card-body">
                <div id="rankTrendChart" style="height: 300px;"></div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    成绩对比
                </h5>
            </div>
            <div class="card-body">
                <div id="scoreComparisonChart" style="height: 300px;"></div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
$(document).ready(function() {
    let currentData = [];
    
    // 初始加载数据
    loadMyRankingData();
    
    // 筛选表单提交
    $('#filterForm').on('submit', function(e) {
        e.preventDefault();
        loadMyRankingData();
    });
    
    // 重置按钮
    $('#resetBtn').on('click', function() {
        $('#filterForm')[0].reset();
        loadMyRankingData();
    });
    
    // 导出按钮
    $('#exportBtn').on('click', function() {
        exportMyData();
    });
});

function loadMyRankingData() {
    showLoading(true);
    
    // 获取筛选条件
    const formData = new FormData($('#filterForm')[0]);
    const params = new URLSearchParams();
    
    for (let [key, value] of formData.entries()) {
        if (value) {
            params.append(key, value);
        }
    }
    
    $.get('/ranking/api/data?' + params.toString())
        .done(function(response) {
            if (response.success) {
                currentData = response.data;
                displayMyRankingData(response.data);
                drawRankTrendChart(response.data);
                drawScoreComparisonChart(response.data);
            } else {
                showError('加载数据失败: ' + response.error);
            }
        })
        .fail(function() {
            showError('网络错误，请稍后重试');
        })
        .always(function() {
            showLoading(false);
        });
}

function displayMyRankingData(data) {
    const tbody = $('#rankingTableBody');
    tbody.empty();
    
    if (data.length === 0) {
        tbody.append(`
            <tr>
                <td colspan="8" class="text-center text-muted">
                    <i class="fas fa-inbox fa-2x mb-2"></i><br>
                    暂无数据
                </td>
            </tr>
        `);
        return;
    }
    
    data.forEach((record) => {
        const row = `
            <tr>
                <td>${record.academic_year}-${record.semester}</td>
                <td>
                    <span class="badge bg-primary">${parseFloat(record.total_score).toFixed(2)}</span>
                </td>
                <td>
                    ${record.total_rank ? 
                        `<span class="badge ${getRankBadgeClass(record.total_rank)}">${record.total_rank}</span>` : 
                        '<span class="text-muted">N/A</span>'
                    }
                </td>
                <td>${parseFloat(record.academic_score).toFixed(2)}</td>
                <td>
                    ${record.academic_rank ? 
                        `<span class="badge bg-success">${record.academic_rank}</span>` : 
                        '<span class="text-muted">N/A</span>'
                    }
                </td>
                <td>${parseFloat(record.comprehensive_score).toFixed(2)}</td>
                <td>
                    ${record.award_level ? 
                        `<span class="badge ${getAwardBadgeClass(record.award_level)}">${record.award_level}</span>` : 
                        '<span class="text-muted">无</span>'
                    }
                </td>
                <td>${record.remarks || ''}</td>
            </tr>
        `;
        tbody.append(row);
    });
}

function getRankBadgeClass(rank) {
    if (rank === 1) return 'bg-warning';
    if (rank === 2) return 'bg-secondary';
    if (rank === 3) return 'bg-info';
    if (rank <= 10) return 'bg-success';
    return 'bg-light text-dark';
}

function getAwardBadgeClass(award) {
    if (award.includes('一等')) return 'bg-warning';
    if (award.includes('二等')) return 'bg-info';
    if (award.includes('三等')) return 'bg-success';
    return 'bg-secondary';
}

function drawRankTrendChart(data) {
    if (data.length === 0) return;
    
    const chart = echarts.init(document.getElementById('rankTrendChart'));
    
    const option = {
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'cross'
            }
        },
        legend: {
            data: ['总排名', '学业排名']
        },
        xAxis: {
            type: 'category',
            data: data.map(item => item.academic_year + '-' + item.semester)
        },
        yAxis: {
            type: 'value',
            name: '排名',
            inverse: true, // 排名越小越好，所以反转Y轴
            min: function(value) {
                return Math.max(1, value.min - 5);
            }
        },
        series: [
            {
                name: '总排名',
                type: 'line',
                data: data.map(item => item.total_rank || null),
                itemStyle: { color: '#4facfe' },
                smooth: true,
                connectNulls: false
            },
            {
                name: '学业排名',
                type: 'line',
                data: data.map(item => item.academic_rank || null),
                itemStyle: { color: '#28a745' },
                smooth: true,
                connectNulls: false
            }
        ]
    };
    chart.setOption(option);
}

function drawScoreComparisonChart(data) {
    if (data.length === 0) return;
    
    const chart = echarts.init(document.getElementById('scoreComparisonChart'));
    
    const option = {
        tooltip: {
            trigger: 'axis'
        },
        legend: {
            data: ['总分', '学业成绩', '综合素质分']
        },
        xAxis: {
            type: 'category',
            data: data.map(item => item.academic_year + '-' + item.semester)
        },
        yAxis: {
            type: 'value',
            name: '分数'
        },
        series: [
            {
                name: '总分',
                type: 'bar',
                data: data.map(item => parseFloat(item.total_score).toFixed(2)),
                itemStyle: { color: '#4facfe' }
            },
            {
                name: '学业成绩',
                type: 'bar',
                data: data.map(item => parseFloat(item.academic_score).toFixed(2)),
                itemStyle: { color: '#28a745' }
            },
            {
                name: '综合素质分',
                type: 'bar',
                data: data.map(item => parseFloat(item.comprehensive_score).toFixed(2)),
                itemStyle: { color: '#ffc107' }
            }
        ]
    };
    chart.setOption(option);
}

function exportMyData() {
    const formData = new FormData($('#filterForm')[0]);
    const params = new URLSearchParams();
    
    for (let [key, value] of formData.entries()) {
        if (value) {
            params.append(key, value);
        }
    }
    
    window.open('/ranking/export?' + params.toString(), '_blank');
}

function showLoading(show) {
    if (show) {
        $('#loadingSpinner').show();
        $('#rankingTable').hide();
    } else {
        $('#loadingSpinner').hide();
        $('#rankingTable').show();
    }
}

function showError(message) {
    $('#rankingTableBody').html(`
        <tr>
            <td colspan="8" class="text-center text-danger">
                <i class="fas fa-exclamation-triangle fa-2x mb-2"></i><br>
                ${message}
            </td>
        </tr>
    `);
}
</script>
{% endblock %}
