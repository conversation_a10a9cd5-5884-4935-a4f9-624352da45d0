#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI服务模块
集成DeepSeek-R1模型，提供智能分析功能
"""

import requests
import json
import logging
from models.database import get_api_config
import os

logger = logging.getLogger(__name__)

class AIService:
    """AI服务类"""
    
    def __init__(self):
        self.api_key = os.getenv("DEEPSEEK_API_KEY", get_api_config('deepseek_api_key', '***********************************'))
        # 修复API端点配置，确保使用正确的聊天完成端点
        base_url = get_api_config('deepseek_api_url', 'https://dashscope.aliyuncs.com/compatible-mode/v1')
        if not base_url.endswith('/chat/completions'):
            if base_url.endswith('/v1'):
                self.api_url = base_url + '/chat/completions'
            else:
                self.api_url = base_url.rstrip('/') + '/chat/completions'
        else:
            self.api_url = base_url
        self.model = get_api_config('deepseek_model', 'deepseek-r1')
        self.temperature = float(get_api_config('ai_temperature', '0.7'))
        self.max_tokens = int(get_api_config('ai_max_tokens', '2000'))
    
    def _make_request(self, messages, temperature=None, max_tokens=None):
        """发送API请求"""
        # 检查API密钥是否配置
        if not self.api_key or self.api_key.strip() == '':
            logger.error("DeepSeek API密钥未配置")
            return "AI服务未配置，请联系管理员配置DeepSeek API密钥。"
        
        headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        }
        
        data = {
            'model': self.model,
            'messages': messages,
            'temperature': temperature or self.temperature,
            'max_tokens': max_tokens or self.max_tokens
        }
        
        try:
            logger.info(f"发送AI请求到: {self.api_url}")
            logger.info(f"使用模型: {self.model}")
            
            response = requests.post(self.api_url, headers=headers, json=data, timeout=30)
            
            # 记录详细的响应信息
            logger.info(f"AI API响应状态码: {response.status_code}")
            
            if response.status_code == 401:
                logger.error("AI API认证失败，请检查API密钥")
                return "AI服务认证失败，请检查API密钥配置。"
            elif response.status_code == 404:
                logger.error(f"AI API端点不存在: {self.api_url}")
                return f"AI服务端点配置错误: {self.api_url}，请联系管理员。"
            elif response.status_code == 429:
                logger.error("AI API请求频率限制")
                return "AI服务请求过于频繁，请稍后重试。"
            
            response.raise_for_status()
            
            result = response.json()
            logger.info("AI API请求成功")
            
            return result['choices'][0]['message']['content']
            
        except requests.exceptions.RequestException as e:
            logger.error(f"AI API请求失败: {e}")
            return f"AI服务暂时不可用，请稍后重试。错误信息: {str(e)}"
        except KeyError as e:
            logger.error(f"AI API响应格式错误: {e}")
            logger.error(f"响应内容: {response.text if 'response' in locals() else 'No response'}")
            return "AI服务响应格式错误，请联系管理员。"
        except Exception as e:
            logger.error(f"AI服务未知错误: {e}")
            return f"AI服务出现未知错误: {str(e)}"
    
    def analyze_student_performance(self, student_data):
        """分析学生学业表现"""
        if not student_data:
            return "没有找到学生数据，无法进行分析。"
        
        # 构建分析数据摘要
        data_summary = self._build_student_summary(student_data)
        
        # 构建分析提示词
        analysis_prompt = get_api_config(
            'analysis_prompt', 
            '请分析该学生的学业表现，包括成绩趋势、排名变化、优势科目等方面。'
        )
        
        messages = [
            {
                "role": "system",
                "content": f"""你是一位专业的教育数据分析师。{analysis_prompt}
                
请从以下几个方面进行分析：
1. 总体成绩趋势分析
2. 排名变化情况
3. 各项评分表现（思想品德、社会工作、科研创新等）
4. 优势和需要改进的方面
5. 具体建议

请用中文回答，语言要专业但易懂，分析要客观准确。"""
            },
            {
                "role": "user",
                "content": f"请分析以下学生的学业数据：\n\n{data_summary}"
            }
        ]
        
        return self._make_request(messages)
    
    def analyze_class_performance(self, class_data, class_name):
        """分析班级整体表现"""
        if not class_data:
            return "没有找到班级数据，无法进行分析。"
        
        # 构建班级数据摘要
        data_summary = self._build_class_summary(class_data, class_name)
        
        messages = [
            {
                "role": "system",
                "content": """你是一位专业的教育数据分析师。请分析班级的整体学业表现。
                
请从以下几个方面进行分析：
1. 班级整体成绩水平
2. 成绩分布情况
3. 优秀学生比例
4. 各项评分的班级平均表现
5. 班级特点和优势
6. 改进建议

请用中文回答，语言要专业但易懂，分析要客观准确。"""
            },
            {
                "role": "user",
                "content": f"请分析以下班级的学业数据：\n\n{data_summary}"
            }
        ]
        
        return self._make_request(messages)
    
    def generate_sql_query(self, user_description):
        """根据用户描述生成SQL查询语句"""
        sql_prompt = get_api_config(
            'sql_prompt',
            '你是一个SQL专家，请根据用户的自然语言描述生成对应的SQL查询语句。'
        )
        
        # 数据库结构说明
        db_schema = """
数据库表结构：
1. students表：学生基本信息
   - student_id: 学号
   - name: 姓名

2. semesters表：学期信息
   - academic_year: 学年（如"2024-2025"）
   - semester: 学期（1或2）

3. majors表：专业信息
   - major_name: 专业名称

4. scores表：成绩记录
   - student_id: 学号
   - class_name: 班级
   - grade: 年级
   - moral_score: 思想品德分数
   - social_work_score: 社会工作分数
   - research_score: 科研创新分数
   - activity_total_score: 集体活动分数
   - collective_score: 集体建设分数
   - comprehensive_score: 综合素质分数
   - academic_score: 学业成绩
   - total_score: 总分
   - total_rank: 总排名
   - academic_rank: 学业排名
   - award_level: 奖学金等级
"""
        
        messages = [
            {
                "role": "system",
                "content": f"""{sql_prompt}

{db_schema}

请根据用户的描述生成准确的SQL查询语句。注意：
1. 使用JOIN连接相关表
2. 确保字段名正确
3. 添加适当的WHERE条件
4. 考虑排序和限制
5. 只返回SQL语句，不要其他解释"""
            },
            {
                "role": "user",
                "content": f"请生成SQL查询：{user_description}"
            }
        ]
        
        return self._make_request(messages, temperature=0.1)  # 降低温度以获得更准确的SQL
    
    def _build_student_summary(self, student_data):
        """构建学生数据摘要"""
        if not student_data:
            return "无数据"
        
        student_info = student_data[0]
        summary = f"""
学生基本信息：
- 学号：{student_info.get('student_id', 'N/A')}
- 姓名：{student_info.get('name', 'N/A')}
- 专业：{student_info.get('major_name', 'N/A')}
- 班级：{student_info.get('class_name', 'N/A')}

各学期成绩记录：
"""
        
        for i, record in enumerate(student_data, 1):
            summary += f"""
第{i}学期（{record.get('academic_year', 'N/A')}-{record.get('semester', 'N/A')}）：
- 总分：{record.get('total_score', 'N/A')}
- 总排名：{record.get('total_rank', 'N/A')}
- 学业成绩：{record.get('academic_score', 'N/A')}
- 学业排名：{record.get('academic_rank', 'N/A')}
- 综合素质分：{record.get('comprehensive_score', 'N/A')}
- 思想品德：{record.get('moral_score', 'N/A')}
- 社会工作：{record.get('social_work_score', 'N/A')}
- 科研创新：{record.get('research_score', 'N/A')}
- 集体活动：{record.get('activity_total_score', 'N/A')}
- 集体建设：{record.get('collective_score', 'N/A')}
- 奖学金等级：{record.get('award_level', 'N/A')}
"""
        
        return summary
    
    def _build_class_summary(self, class_data, class_name):
        """构建班级数据摘要"""
        if not class_data:
            return "无数据"
        
        # 计算统计信息
        total_students = len(class_data)
        total_scores = [s.get('total_score', 0) for s in class_data if s.get('total_score')]
        academic_scores = [s.get('academic_score', 0) for s in class_data if s.get('academic_score')]
        
        avg_total = sum(total_scores) / len(total_scores) if total_scores else 0
        avg_academic = sum(academic_scores) / len(academic_scores) if academic_scores else 0
        
        # 统计奖学金等级
        award_levels = {}
        for student in class_data:
            level = student.get('award_level', '无')
            award_levels[level] = award_levels.get(level, 0) + 1
        
        summary = f"""
班级基本信息：
- 班级名称：{class_name}
- 学生总数：{total_students}
- 平均总分：{avg_total:.2f}
- 平均学业成绩：{avg_academic:.2f}

奖学金等级分布：
"""
        
        for level, count in award_levels.items():
            summary += f"- {level}：{count}人\n"
        
        summary += "\n学生详细成绩：\n"
        
        # 按总分排序显示前10名
        sorted_students = sorted(class_data, key=lambda x: x.get('total_score', 0), reverse=True)
        for i, student in enumerate(sorted_students[:10], 1):
            summary += f"{i}. {student.get('name', 'N/A')}（{student.get('student_id', 'N/A')}）- 总分：{student.get('total_score', 'N/A')}\n"
        
        return summary
