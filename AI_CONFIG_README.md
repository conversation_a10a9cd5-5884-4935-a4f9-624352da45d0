# AI服务配置说明

## 阿里云百炼 API 配置

本系统集成了DeepSeek R1模型（通过阿里云百炼平台），用于提供智能分析和SQL查询助手功能。

### 1. 获取API密钥

1. 访问 [阿里云百炼控制台](https://bailian.console.aliyun.com/)
2. 注册账号并登录
3. 在控制台中创建API密钥
4. 复制生成的API密钥（格式类似：`sk-xxxxxxxxxxxxxxxxxxxx`）

### 2. 配置方法

#### 方法一：环境变量配置（推荐）
在系统环境变量中设置：
```
DEEPSEEK_API_KEY=your_api_key_here
```

#### 方法二：数据库配置
1. 登录系统管理界面
2. 进入"API管理"页面
3. 设置以下配置项：
   - `deepseek_api_key`: 您的阿里云百炼API密钥
   - `deepseek_api_url`: `https://dashscope.aliyuncs.com/compatible-mode/v1`
   - `deepseek_model`: `deepseek-r1`（或其他可用模型）

### 3. 配置验证

配置完成后，可以在排行榜页面测试AI助手功能：
1. 点击"AI助手"按钮
2. 输入查询需求，如："查询总分前10名的学生"
3. 点击"生成SQL"按钮
4. 如果配置正确，系统会生成相应的SQL语句

### 4. 常见问题

#### 问题1：认证失败
- 错误信息：`AI服务认证失败，请检查API密钥配置`
- 解决方案：检查API密钥是否正确，确保没有多余的空格或特殊字符

#### 问题2：端点配置错误
- 错误信息：`AI服务端点配置错误`
- 解决方案：确认API端点设置为 `https://dashscope.aliyuncs.com/compatible-mode/v1`

#### 问题3：请求频率限制
- 错误信息：`AI服务请求过于频繁`
- 解决方案：等待一段时间后再尝试，或联系DeepSeek升级API额度

#### 问题4：API密钥未配置
- 错误信息：`AI服务未配置，请联系管理员配置DeepSeek API密钥`
- 解决方案：按照上述方法配置API密钥

### 5. 模型参数调优

可以在数据库中配置以下参数来优化AI响应：

- `ai_temperature`: 控制回答的随机性（0.1-1.0，默认0.7）
- `ai_max_tokens`: 最大响应长度（默认2000）
- `analysis_prompt`: 分析提示词模板
- `sql_prompt`: SQL生成提示词模板

### 6. 支持的功能

- **智能SQL查询生成**：根据自然语言描述生成数据库查询语句
- **学生成绩分析**：提供个人学业表现分析报告
- **班级整体分析**：分析班级整体学业情况

### 7. 注意事项

1. API密钥请妥善保管，不要泄露给他人
2. 请遵守阿里云百炼的使用条款和API调用限制
3. 建议定期检查API使用量，避免超出限额
4. 生产环境建议使用环境变量配置，提高安全性

### 8. 技术支持

如遇到其他问题，请：
1. 检查系统日志文件获取详细错误信息
2. 确认阿里云百炼API服务状态
3. 联系系统管理员或技术支持

### 9. 已完成的修复

✅ **问题已解决**：
- AI服务端点已配置为正确的阿里云百炼API
- 支持多字段排序功能
- 修复列显示配置功能
- 完善重置功能

🎯 **当前配置**：
- API端点：`https://dashscope.aliyuncs.com/compatible-mode/v1`
- 模型：`deepseek-r1`
- API密钥：已预配置（如需更换请联系管理员） 