{% extends "base.html" %}

{% block title %}个人面板 - 学生学业数据管理与分析平台{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="h3 mb-4">
            <i class="fas fa-user-graduate me-2"></i>
            个人学业数据
        </h1>
    </div>
</div>

<!-- 个人信息卡片 -->
{% if student_data %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-id-card me-2"></i>
                    基本信息
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <strong>学号：</strong>{{ student_data[0].student_id }}
                    </div>
                    <div class="col-md-3">
                        <strong>姓名：</strong>{{ student_data[0].name }}
                    </div>
                    <div class="col-md-3">
                        <strong>专业：</strong>{{ student_data[0].major_name }}
                    </div>
                    <div class="col-md-3">
                        <strong>班级：</strong>{{ student_data[0].class_name }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 最新成绩概览 -->
{% set latest_record = student_data[-1] %}
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ "%.2f"|format(latest_record.total_score) }}</h4>
                        <p class="card-text">总分</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-star fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ latest_record.total_rank or 'N/A' }}</h4>
                        <p class="card-text">总排名</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-trophy fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ "%.2f"|format(latest_record.academic_score) }}</h4>
                        <p class="card-text">学业成绩</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-book fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ "%.2f"|format(latest_record.comprehensive_score) }}</h4>
                        <p class="card-text">综合素质</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-chart-pie fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 成绩趋势图表 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-line me-2"></i>
                    成绩趋势
                </h5>
            </div>
            <div class="card-body">
                <div id="trendChart" style="height: 400px;"></div>
            </div>
        </div>
    </div>
</div>

<!-- 成绩详细记录 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-table me-2"></i>
                    历史成绩记录
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>学年学期</th>
                                <th>总分</th>
                                <th>总排名</th>
                                <th>学业成绩</th>
                                <th>学业排名</th>
                                <th>综合素质</th>
                                <th>奖学金等级</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for record in student_data %}
                            <tr>
                                <td>{{ record.academic_year }}-{{ record.semester }}</td>
                                <td>
                                    <span class="badge bg-primary">{{ "%.2f"|format(record.total_score) }}</span>
                                </td>
                                <td>
                                    {% if record.total_rank %}
                                        {% if record.total_rank <= 3 %}
                                            <span class="badge bg-warning">{{ record.total_rank }}</span>
                                        {% elif record.total_rank <= 10 %}
                                            <span class="badge bg-info">{{ record.total_rank }}</span>
                                        {% else %}
                                            <span class="badge bg-secondary">{{ record.total_rank }}</span>
                                        {% endif %}
                                    {% else %}
                                        <span class="text-muted">N/A</span>
                                    {% endif %}
                                </td>
                                <td>{{ "%.2f"|format(record.academic_score) }}</td>
                                <td>
                                    {% if record.academic_rank %}
                                        <span class="badge bg-success">{{ record.academic_rank }}</span>
                                    {% else %}
                                        <span class="text-muted">N/A</span>
                                    {% endif %}
                                </td>
                                <td>{{ "%.2f"|format(record.comprehensive_score) }}</td>
                                <td>
                                    {% if record.award_level %}
                                        {% if record.award_level == '一等奖学金' %}
                                            <span class="badge bg-warning">{{ record.award_level }}</span>
                                        {% elif record.award_level == '二等奖学金' %}
                                            <span class="badge bg-info">{{ record.award_level }}</span>
                                        {% elif record.award_level == '三等奖学金' %}
                                            <span class="badge bg-success">{{ record.award_level }}</span>
                                        {% else %}
                                            <span class="badge bg-secondary">{{ record.award_level }}</span>
                                        {% endif %}
                                    {% else %}
                                        <span class="text-muted">无</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 成绩分析 -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-pie me-2"></i>
                    成绩构成分析
                </h5>
            </div>
            <div class="card-body">
                <div id="scoreCompositionChart" style="height: 300px;"></div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    各项得分对比
                </h5>
            </div>
            <div class="card-body">
                <div id="scoreComparisonChart" style="height: 300px;"></div>
            </div>
        </div>
    </div>
</div>

{% else %}
<div class="row">
    <div class="col-12">
        <div class="alert alert-warning">
            <i class="fas fa-exclamation-triangle me-2"></i>
            暂无成绩数据，请联系管理员确认您的学号是否正确绑定。
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_scripts %}
<script>
$(document).ready(function() {
    {% if student_data %}
    // 绘制成绩趋势图
    drawTrendChart();
    // 绘制成绩构成图
    drawScoreCompositionChart();
    // 绘制成绩对比图
    drawScoreComparisonChart();
    {% endif %}
});

{% if student_data %}
function drawTrendChart() {
    const chart = echarts.init(document.getElementById('trendChart'));
    const data = {{ student_data | tojson }};
    
    const option = {
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'cross'
            }
        },
        legend: {
            data: ['总分', '学业成绩', '综合素质分']
        },
        xAxis: {
            type: 'category',
            data: data.map(item => item.academic_year + '-' + item.semester)
        },
        yAxis: {
            type: 'value',
            name: '分数'
        },
        series: [
            {
                name: '总分',
                type: 'line',
                data: data.map(item => item.total_score.toFixed(2)),
                itemStyle: { color: '#4facfe' },
                smooth: true
            },
            {
                name: '学业成绩',
                type: 'line',
                data: data.map(item => item.academic_score.toFixed(2)),
                itemStyle: { color: '#28a745' },
                smooth: true
            },
            {
                name: '综合素质分',
                type: 'line',
                data: data.map(item => item.comprehensive_score.toFixed(2)),
                itemStyle: { color: '#ffc107' },
                smooth: true
            }
        ]
    };
    chart.setOption(option);
}

function drawScoreCompositionChart() {
    const chart = echarts.init(document.getElementById('scoreCompositionChart'));
    const latestData = {{ student_data[-1] | tojson }};
    
    const option = {
        tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        series: [{
            name: '成绩构成',
            type: 'pie',
            radius: '70%',
            data: [
                { name: '学业成绩', value: latestData.academic_score },
                { name: '综合素质分', value: latestData.comprehensive_score }
            ],
            emphasis: {
                itemStyle: {
                    shadowBlur: 10,
                    shadowOffsetX: 0,
                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
            }
        }]
    };
    chart.setOption(option);
}

function drawScoreComparisonChart() {
    const chart = echarts.init(document.getElementById('scoreComparisonChart'));
    const latestData = {{ student_data[-1] | tojson }};
    
    const option = {
        tooltip: {
            trigger: 'axis'
        },
        xAxis: {
            type: 'category',
            data: ['德育分', '社会工作分', '科研分', '活动总分', '集体分']
        },
        yAxis: {
            type: 'value'
        },
        series: [{
            name: '得分',
            type: 'bar',
            data: [
                latestData.moral_score || 0,
                latestData.social_work_score || 0,
                latestData.research_score || 0,
                latestData.activity_total_score || 0,
                latestData.collective_score || 0
            ],
            itemStyle: {
                color: function(params) {
                    const colors = ['#4facfe', '#28a745', '#ffc107', '#17a2b8', '#6f42c1'];
                    return colors[params.dataIndex];
                }
            }
        }]
    };
    chart.setOption(option);
}
{% endif %}
</script>
{% endblock %}
